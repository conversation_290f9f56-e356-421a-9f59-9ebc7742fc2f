{"doc": " ES分页结果\n\n <AUTHOR>\n", "fields": [{"name": "pageNum", "doc": " 当前页码\n"}, {"name": "pageSize", "doc": " 每页大小\n"}, {"name": "total", "doc": " 总记录数\n"}, {"name": "pages", "doc": " 总页数\n"}, {"name": "records", "doc": " 数据列表\n"}, {"name": "aggregations", "doc": " 聚合结果\n"}, {"name": "isFirstPage", "doc": " 是否为第一页\n"}, {"name": "isLastPage", "doc": " 是否为最后一页\n"}, {"name": "has<PERSON>revious", "doc": " 是否有前一页\n"}, {"name": "hasNext", "doc": " 是否有下一页\n"}], "enumConstants": [], "methods": [{"name": "calculatePageInfo", "paramTypes": [], "doc": " 计算分页信息\n"}, {"name": "of", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long", "java.util.List"], "doc": " 构造分页结果\n"}], "constructors": []}