{"doc": " 对象存储配置Service业务层处理\n\n <AUTHOR>\n <AUTHOR>\n @date 2021-08-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "init", "paramTypes": [], "doc": " 项目启动时，初始化参数到缓存，加载配置类\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.system.domain.SysOssConfig"], "doc": " 保存前的数据校验\n"}, {"name": "checkConfigKeyUnique", "paramTypes": ["org.dromara.system.domain.SysOssConfig"], "doc": " 判断configKey是否唯一\n"}, {"name": "updateOssConfigStatus", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": " 启用禁用状态\n"}], "constructors": []}