{"doc": " RAG知识库服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "processDocumentAsync", "paramTypes": ["java.lang.Long"], "doc": " 异步处理文档\n"}, {"name": "chunkDocument", "paramTypes": ["java.lang.String"], "doc": " 文档分块\n"}, {"name": "updateKnowledgeBaseVectorCount", "paramTypes": ["java.lang.Long"], "doc": " 更新知识库向量数量\n"}, {"name": "updateKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": " 更新知识库统计信息\n"}, {"name": "vectorArrayToString", "paramTypes": ["float[]"], "doc": " 向量数组转换为字符串\n"}], "constructors": []}