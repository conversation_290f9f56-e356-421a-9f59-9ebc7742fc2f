{"doc": " 公告 信息操作处理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取通知公告列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据通知公告编号获取详细信息\n\n @param noticeId 公告ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": " 新增通知公告\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": " 修改通知公告\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除通知公告\n\n @param noticeIds 公告ID串\n"}], "constructors": []}