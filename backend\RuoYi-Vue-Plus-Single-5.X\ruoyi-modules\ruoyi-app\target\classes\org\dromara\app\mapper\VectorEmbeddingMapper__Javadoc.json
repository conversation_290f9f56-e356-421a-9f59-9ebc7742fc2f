{"doc": " 向量嵌入Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "searchSimilarVectors", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.Integer", "java.lang.Double"], "doc": " 向量相似度搜索\n\n @param knowledgeBaseId 知识库ID\n @param queryVector 查询向量\n @param limit 返回数量限制\n @param threshold 相似度阈值\n @return 相似文档列表\n"}, {"name": "hybridSearch", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Double"], "doc": " 混合搜索（向量相似度 + 文本匹配）\n\n @param knowledgeBaseId 知识库ID\n @param queryVector 查询向量\n @param keywords 关键词\n @param limit 返回数量限制\n @param threshold 相似度阈值\n @return 相似文档列表\n"}, {"name": "selectByDocumentId", "paramTypes": ["java.lang.Long"], "doc": " 根据文档ID查询向量\n\n @param documentId 文档ID\n @return 向量列表\n"}, {"name": "selectByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": " 根据知识库ID查询向量\n\n @param knowledgeBaseId 知识库ID\n @return 向量列表\n"}, {"name": "countByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": " 统计知识库向量数量\n\n @param knowledgeBaseId 知识库ID\n @return 向量数量\n"}, {"name": "countByDocumentId", "paramTypes": ["java.lang.Long"], "doc": " 统计文档向量数量\n\n @param documentId 文档ID\n @return 向量数量\n"}, {"name": "deleteByDocumentId", "paramTypes": ["java.lang.Long"], "doc": " 根据文档ID删除向量\n\n @param documentId 文档ID\n @return 删除数量\n"}, {"name": "deleteByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": " 根据知识库ID删除向量\n\n @param knowledgeBaseId 知识库ID\n @return 删除数量\n"}, {"name": "batchInsert", "paramTypes": ["java.util.List"], "doc": " 批量插入向量\n\n @param embeddings 向量列表\n @return 插入数量\n"}, {"name": "selectByChunkType", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 根据文本块类型查询向量\n\n @param knowledgeBaseId 知识库ID\n @param chunkType 文本块类型\n @return 向量列表\n"}], "constructors": []}