{"doc": " ES搜索请求\n\n <AUTHOR>\n", "fields": [{"name": "indexName", "doc": " 索引名称\n"}, {"name": "keyword", "doc": " 查询关键字\n"}, {"name": "fields", "doc": " 查询字段\n"}, {"name": "filters", "doc": " 过滤条件\n"}, {"name": "sorts", "doc": " 排序字段\n"}, {"name": "highlightFields", "doc": " 高亮字段\n"}, {"name": "pageNum", "doc": " 页码\n"}, {"name": "pageSize", "doc": " 页大小\n"}, {"name": "fetchSource", "doc": " 是否返回源数据\n"}, {"name": "includeFields", "doc": " 包含字段\n"}, {"name": "excludeFields", "doc": " 排除字段\n"}, {"name": "aggregations", "doc": " 聚合配置\n"}], "enumConstants": [], "methods": [], "constructors": []}