{"doc": " 菜单 业务层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectMenuList", "paramTypes": ["java.lang.Long"], "doc": " 根据用户查询系统菜单列表\n\n @param userId 用户ID\n @return 菜单列表\n"}, {"name": "selectMenuList", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo", "java.lang.Long"], "doc": " 根据用户查询系统菜单列表\n\n @param menu   菜单信息\n @param userId 用户ID\n @return 菜单列表\n"}, {"name": "selectMenuPermsByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询权限\n\n @param userId 用户ID\n @return 权限列表\n"}, {"name": "selectMenuPermsByRoleId", "paramTypes": ["java.lang.Long"], "doc": " 根据角色ID查询权限\n\n @param roleId 角色ID\n @return 权限列表\n"}, {"name": "selectMenuTreeByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询菜单树信息\n\n @param userId 用户ID\n @return 菜单列表\n"}, {"name": "selectMenuListByRoleId", "paramTypes": ["java.lang.Long"], "doc": " 根据角色ID查询菜单树信息\n\n @param roleId 角色ID\n @return 选中菜单列表\n"}, {"name": "buildMenus", "paramTypes": ["java.util.List"], "doc": " 构建前端路由所需要的菜单\n\n @param menus 菜单列表\n @return 路由列表\n"}, {"name": "buildMenuTreeSelect", "paramTypes": ["java.util.List"], "doc": " 构建前端所需要下拉树结构\n\n @param menus 菜单列表\n @return 下拉树结构列表\n"}, {"name": "selectMenuById", "paramTypes": ["java.lang.Long"], "doc": " 根据菜单ID查询信息\n\n @param menuId 菜单ID\n @return 菜单信息\n"}, {"name": "hasChildByMenuId", "paramTypes": ["java.lang.Long"], "doc": " 是否存在菜单子节点\n\n @param menuId 菜单ID\n @return 结果 true 存在 false 不存在\n"}, {"name": "hasChildByMenuId", "paramTypes": ["java.util.List"], "doc": " 是否存在菜单子节点\n\n @param menuIds 菜单ID串\n @return 结果 true 存在 false 不存在\n"}, {"name": "checkMenuExistRole", "paramTypes": ["java.lang.Long"], "doc": " 查询菜单是否存在角色\n\n @param menuId 菜单ID\n @return 结果 true 存在 false 不存在\n"}, {"name": "insertMenu", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": " 新增保存菜单信息\n\n @param bo 菜单信息\n @return 结果\n"}, {"name": "updateMenu", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": " 修改保存菜单信息\n\n @param bo 菜单信息\n @return 结果\n"}, {"name": "deleteMenuById", "paramTypes": ["java.lang.Long"], "doc": " 删除菜单管理信息\n\n @param menuId 菜单ID\n @return 结果\n"}, {"name": "deleteMenuById", "paramTypes": ["java.util.List"], "doc": " 批量删除菜单管理信息\n\n @param menuIds 菜单ID串\n @return 结果\n"}, {"name": "checkMenuNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": " 校验菜单名称是否唯一\n\n @param menu 菜单信息\n @return 结果\n"}], "constructors": []}