{"doc": " 系统访问记录\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取系统访问记录列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysLogininforBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出系统访问记录列表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除登录日志\n @param infoIds 日志ids\n"}, {"name": "clean", "paramTypes": [], "doc": " 清理系统访问记录\n"}], "constructors": []}