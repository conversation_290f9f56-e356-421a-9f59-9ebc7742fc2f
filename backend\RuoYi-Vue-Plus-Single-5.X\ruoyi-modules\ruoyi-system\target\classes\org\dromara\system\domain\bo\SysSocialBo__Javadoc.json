{"doc": " 社会化关系业务对象 sys_social\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 主键\n"}, {"name": "authId", "doc": " 认证唯一ID\n"}, {"name": "source", "doc": " 用户来源\n"}, {"name": "accessToken", "doc": " 用户的授权令牌\n"}, {"name": "expireIn", "doc": " 用户的授权令牌的有效期，部分平台可能没有\n"}, {"name": "refreshToken", "doc": " 刷新令牌，部分平台可能没有\n"}, {"name": "openId", "doc": " 平台唯一id\n"}, {"name": "userId", "doc": " 用户的 ID\n"}, {"name": "accessCode", "doc": " 平台的授权信息，部分平台可能没有\n"}, {"name": "unionId", "doc": " 用户的 unionid\n"}, {"name": "scope", "doc": " 授予的权限，部分平台可能没有\n"}, {"name": "userName", "doc": " 授权的第三方账号\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 授权的第三方昵称\n"}, {"name": "email", "doc": " 授权的第三方邮箱\n"}, {"name": "avatar", "doc": " 授权的第三方头像地址\n"}, {"name": "tokenType", "doc": " 个别平台的授权信息，部分平台可能没有\n"}, {"name": "idToken", "doc": " id token，部分平台可能没有\n"}, {"name": "macAlgorithm", "doc": " 小米平台用户的附带属性，部分平台可能没有\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " 小米平台用户的附带属性，部分平台可能没有\n"}, {"name": "code", "doc": " 用户的授权code，部分平台可能没有\n"}, {"name": "oauthToken", "doc": " Twitter平台用户的附带属性，部分平台可能没有\n"}, {"name": "oauthTokenSecret", "doc": " Twitter平台用户的附带属性，部分平台可能没有\n"}], "enumConstants": [], "methods": [], "constructors": []}