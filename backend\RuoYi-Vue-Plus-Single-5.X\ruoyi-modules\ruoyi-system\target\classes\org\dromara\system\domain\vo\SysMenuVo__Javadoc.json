{"doc": " 菜单权限视图对象 sys_menu\n\n <AUTHOR>\n", "fields": [{"name": "menuId", "doc": " 菜单ID\n"}, {"name": "menuName", "doc": " 菜单名称\n"}, {"name": "parentId", "doc": " 父菜单ID\n"}, {"name": "orderNum", "doc": " 显示顺序\n"}, {"name": "path", "doc": " 路由地址\n"}, {"name": "component", "doc": " 组件路径\n"}, {"name": "queryParam", "doc": " 路由参数\n"}, {"name": "isFrame", "doc": " 是否为外链（0是 1否）\n"}, {"name": "isCache", "doc": " 是否缓存（0缓存 1不缓存）\n"}, {"name": "menuType", "doc": " 菜单类型（M目录 C菜单 F按钮）\n"}, {"name": "visible", "doc": " 显示状态（0显示 1隐藏）\n"}, {"name": "status", "doc": " 菜单状态（0正常 1停用）\n"}, {"name": "perms", "doc": " 权限标识\n"}, {"name": "icon", "doc": " 菜单图标\n"}, {"name": "createDept", "doc": " 创建部门\n"}, {"name": "remark", "doc": " 备注\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "children", "doc": " 子菜单\n"}], "enumConstants": [], "methods": [], "constructors": []}