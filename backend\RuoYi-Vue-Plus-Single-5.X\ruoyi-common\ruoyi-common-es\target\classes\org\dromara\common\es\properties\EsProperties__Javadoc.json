{"doc": " ES配置属性\n\n <AUTHOR>\n", "fields": [{"name": "enabled", "doc": " 是否开启\n"}, {"name": "hosts", "doc": " 集群节点地址\n"}, {"name": "username", "doc": " 用户名\n"}, {"name": "password", "doc": " 密码\n"}, {"name": "connectTimeout", "doc": " 连接超时时间（毫秒）\n"}, {"name": "socketTimeout", "doc": " 响应超时时间（毫秒）\n"}, {"name": "connectionRequestTimeout", "doc": " 连接请求超时时间（毫秒）\n"}, {"name": "maxConnections", "doc": " 最大连接数\n"}, {"name": "maxConnectionsPerRoute", "doc": " 每个路由的最大连接数\n"}, {"name": "defaultShards", "doc": " 索引默认分片数\n"}, {"name": "defaultReplicas", "doc": " 索引默认副本数\n"}, {"name": "sslEnabled", "doc": " 是否启用SSL\n"}, {"name": "debugEnabled", "doc": " 是否启用调试日志\n"}], "enumConstants": [], "methods": [], "constructors": []}