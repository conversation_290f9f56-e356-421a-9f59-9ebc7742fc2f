{"doc": " 基于LangChain4j的聊天服务实现类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "callAgentService", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": " 调用特定类型的Agent服务（同步）\n\n @param agentType Agent类型\n @param message 用户消息\n @param params 额外参数\n @param userId 用户ID\n @return 处理结果\n"}, {"name": "callAgentServiceStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.Long"], "doc": " 调用特定类型的Agent服务（流式）\n\n @param agentType Agent类型\n @param message 用户消息\n @param params 额外参数\n @param userId 用户ID\n @return SSE流\n"}, {"name": "invokeAgentService", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 根据Agent类型调用相应的服务\n"}, {"name": "invokeStreamingAgentService", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "dev.langchain4j.model.chat.response.StreamingChatResponseHandler"], "doc": " 调用流式Agent服务\n"}, {"name": "createAgentContext", "paramTypes": ["java.lang.String", "java.lang.Long", "java.lang.String"], "doc": " 创建Agent上下文\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": " 获取或创建内存\n"}, {"name": "loadHistoryToMemory", "paramTypes": ["java.lang.String", "dev.langchain4j.memory.chat.MessageWindowChatMemory"], "doc": " 从数据库加载历史消息到内存\n"}, {"name": "enhanceMemoryWithSystemPrompt", "paramTypes": ["dev.langchain4j.memory.chat.MessageWindowChatMemory", "java.lang.String"], "doc": " 根据Agent类型增强内存的系统提示词\n"}, {"name": "enhanceMessageWithRAG", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 使用RAG增强消息\n"}, {"name": "buildMetadata", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto", "org.dromara.app.domain.Agent"], "doc": " 构建消息元数据\n"}], "constructors": []}