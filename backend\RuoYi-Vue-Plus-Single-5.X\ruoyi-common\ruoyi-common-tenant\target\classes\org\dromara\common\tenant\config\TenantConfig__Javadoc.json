{"doc": " 租户配置类\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "tenantInit", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor", "org.dromara.common.tenant.properties.TenantProperties"], "doc": " 初始化租户配置\n"}, {"name": "tenantLineInnerInterceptor", "paramTypes": ["org.dromara.common.tenant.properties.TenantProperties"], "doc": " 多租户插件\n"}, {"name": "tenantCacheManager", "paramTypes": [], "doc": " 多租户缓存管理器\n"}, {"name": "tenantSaTokenDao", "paramTypes": [], "doc": " 多租户鉴权dao实现\n"}], "constructors": []}