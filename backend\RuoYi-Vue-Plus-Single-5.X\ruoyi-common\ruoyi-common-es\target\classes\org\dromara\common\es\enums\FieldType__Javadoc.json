{"doc": " ES字段类型枚举\n\n <AUTHOR>\n", "fields": [], "enumConstants": [{"name": "TEXT", "doc": " 文本类型\n"}, {"name": "KEYWORD", "doc": " 关键字类型\n"}, {"name": "INTEGER", "doc": " 整数类型\n"}, {"name": "LONG", "doc": " 长整数类型\n"}, {"name": "FLOAT", "doc": " 浮点数类型\n"}, {"name": "DOUBLE", "doc": " 双精度浮点数类型\n"}, {"name": "BOOLEAN", "doc": " 布尔类型\n"}, {"name": "DATE", "doc": " 日期类型\n"}, {"name": "OBJECT", "doc": " 对象类型\n"}, {"name": "NESTED", "doc": " 嵌套类型\n"}, {"name": "GEO_POINT", "doc": " 地理位置类型\n"}, {"name": "IP", "doc": " IP地址类型\n"}], "methods": [], "constructors": []}