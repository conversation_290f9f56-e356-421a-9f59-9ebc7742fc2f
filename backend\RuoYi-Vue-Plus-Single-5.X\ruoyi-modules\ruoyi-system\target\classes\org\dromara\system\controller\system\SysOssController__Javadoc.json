{"doc": " 文件上传 控制层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysOssBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询OSS对象存储列表\n"}, {"name": "listByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 查询OSS对象基于id串\n\n @param ossIds OSS对象ID串\n"}, {"name": "upload", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 上传OSS对象存储\n\n @param file 文件\n"}, {"name": "download", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 下载OSS对象\n\n @param ossId OSS对象ID\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除OSS对象存储\n\n @param ossIds OSS对象ID串\n"}], "constructors": []}