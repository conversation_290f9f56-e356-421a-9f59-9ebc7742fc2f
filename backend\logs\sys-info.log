2025-07-16 16:26:05 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-16 16:26:06 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 40308 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-07-16 16:26:06 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-07-16 16:26:23 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 16:26:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-16 16:26:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-16 16:26:46 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@80081d7
2025-07-16 16:26:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-16 16:26:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-16 16:26:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-16 16:26:52 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-07-16 16:26:52 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-16 16:26:54 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for *************/*************:6379
2025-07-16 16:26:54 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for *************/*************:6379
2025-07-16 16:26:56 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 初始化默认AI工具
2025-07-16 16:26:57 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 创建时间工具
2025-07-16 16:26:57 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 创建文件操作工具
2025-07-16 16:26:57 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 注册默认工具执行器
2025-07-16 16:26:57 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: calculator - 计算器
2025-07-16 16:26:57 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-07-16 16:26:57 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-07-16 16:26:57 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 默认工具执行器注册完成
2025-07-16 16:26:58 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 初始化默认AI代理配置
2025-07-16 16:26:58 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 数据库中已存在 6 个代理，跳过初始化
2025-07-16 16:26:59 [main] INFO  o.d.c.mongodb.config.MongoConfig - 初始化 MongoDB 配置
2025-07-16 16:27:00 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/17.0.10+11-LTS-240"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@5c47ef5f, com.mongodb.Jep395RecordCodecProvider@2f04c1f3, com.mongodb.KotlinCodecProvider@56d5a50f]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=15000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=30000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=50, minSize=5, maxWaitTimeMS=30000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=600000, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-07-16 16:27:05 [main] INFO  o.d.c.caffeine.config.CaffeineConfig - 初始化Caffeine缓存成功，最大容量: 1000, 过期时间: 3600秒
2025-07-16 16:27:08 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@bd2f5a9
2025-07-16 16:27:10 [main] INFO  o.d.c.p.config.PayAutoConfiguration - 支付组件自动配置已启用
2025-07-16 16:27:13 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-16 16:27:13 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 16:27:14 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 16:27:14 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 16:27:14 [main] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-16 16:27:14 [main] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-07-16 16:27:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-16 16:27:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-07-16 16:27:27 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-07-16 16:27:27 [main] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-07-16 16:27:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-16 16:28:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-16 16:28:52 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 36068 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-07-16 16:28:52 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-07-16 16:29:09 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-16 16:29:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-16 16:29:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-16 16:29:32 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@********
2025-07-16 16:29:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-16 16:29:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-16 16:29:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-16 16:29:37 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-07-16 16:29:37 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-16 16:29:38 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for *************/*************:6379
2025-07-16 16:29:39 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for *************/*************:6379
2025-07-16 16:29:40 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 初始化默认AI工具
2025-07-16 16:29:41 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 创建时间工具
2025-07-16 16:29:41 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 创建文件操作工具
2025-07-16 16:29:41 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 注册默认工具执行器
2025-07-16 16:29:41 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: calculator - 计算器
2025-07-16 16:29:41 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-07-16 16:29:41 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-07-16 16:29:41 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 默认工具执行器注册完成
2025-07-16 16:29:42 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 初始化默认AI代理配置
2025-07-16 16:29:42 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 数据库中已存在 6 个代理，跳过初始化
2025-07-16 16:29:43 [main] INFO  o.d.c.mongodb.config.MongoConfig - 初始化 MongoDB 配置
2025-07-16 16:29:43 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/17.0.10+11-LTS-240"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@f7b6b9e, com.mongodb.Jep395RecordCodecProvider@775429c8, com.mongodb.KotlinCodecProvider@7923cb8d]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=15000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=30000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=50, minSize=5, maxWaitTimeMS=30000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=600000, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-07-16 16:29:47 [main] INFO  o.d.c.caffeine.config.CaffeineConfig - 初始化Caffeine缓存成功，最大容量: 1000, 过期时间: 3600秒
2025-07-16 16:29:49 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4525d1d3
2025-07-16 16:29:50 [main] INFO  o.d.c.p.config.PayAutoConfiguration - 支付组件自动配置已启用
2025-07-16 16:29:52 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-16 16:29:53 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-16 16:29:53 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-16 16:29:53 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-16 16:30:04 [cluster-ClusterId{value='687762f7ffa43e2c05ac6720', description='null'}-**************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server **************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.lambda$open$0(SocketStream.java:85)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:85)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:233)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:219)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.ConnectException: Connection timed out: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:76)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:104)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:79)
	... 3 common frames omitted
2025-07-16 16:30:35 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 106.541 seconds (process running for 108.888)
2025-07-16 16:30:35 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-16 16:30:35 [main] INFO  o.d.a.c.ToolInitializationConfig - 开始初始化AI工具系统...
2025-07-16 16:30:35 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 开始初始化系统工具
2025-07-16 16:30:35 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 初始化默认AI工具
2025-07-16 16:30:36 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 创建时间工具
2025-07-16 16:30:36 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 创建文件操作工具
2025-07-16 16:30:36 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 注册默认工具执行器
2025-07-16 16:30:36 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: calculator - 计算器
2025-07-16 16:30:36 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-07-16 16:30:36 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-07-16 16:30:36 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 默认工具执行器注册完成
2025-07-16 16:30:36 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 系统工具初始化完成
2025-07-16 16:30:36 [main] INFO  o.d.a.c.ToolInitializationConfig - AI工具系统初始化完成
2025-07-16 16:30:36 [main] INFO  o.d.app.config.PaymentTimeoutConfig - 初始化支付超时队列监听器，队列名：payment:timeout:queue
2025-07-16 16:30:36 [main] INFO  o.d.app.config.PaymentTimeoutConfig - 支付超时队列监听器初始化成功
2025-07-16 16:30:36 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
