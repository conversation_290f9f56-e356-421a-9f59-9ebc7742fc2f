{"doc": " 字典 业务层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectDictDataList", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": " 根据条件分页查询字典数据\n\n @param dictData 字典数据信息\n @return 字典数据集合信息\n"}, {"name": "selectDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据字典类型和字典键值查询字典数据信息\n\n @param dictType  字典类型\n @param dictValue 字典键值\n @return 字典标签\n"}, {"name": "selectDictDataById", "paramTypes": ["java.lang.Long"], "doc": " 根据字典数据ID查询信息\n\n @param dictCode 字典数据ID\n @return 字典数据\n"}, {"name": "deleteDictDataByIds", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除字典数据信息\n\n @param dictCodes 需要删除的字典数据ID\n"}, {"name": "insertDictData", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": " 新增保存字典数据信息\n\n @param bo 字典数据信息\n @return 结果\n"}, {"name": "updateDictData", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": " 修改保存字典数据信息\n\n @param bo 字典数据信息\n @return 结果\n"}, {"name": "checkDictDataUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": " 校验字典键值是否唯一\n\n @param dict 字典数据\n @return 结果\n"}], "constructors": []}