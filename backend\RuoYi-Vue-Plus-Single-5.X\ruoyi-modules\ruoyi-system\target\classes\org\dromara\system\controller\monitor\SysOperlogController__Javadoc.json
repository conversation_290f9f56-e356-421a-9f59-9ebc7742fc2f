{"doc": " 操作日志记录\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取操作日志记录列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出操作日志记录列表\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 批量删除操作日志记录\n @param operIds 日志ids\n"}, {"name": "clean", "paramTypes": [], "doc": " 清理操作日志记录\n"}], "constructors": []}