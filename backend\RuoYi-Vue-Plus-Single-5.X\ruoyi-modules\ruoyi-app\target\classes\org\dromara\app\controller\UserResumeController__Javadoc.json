{"doc": " 用户简历管理控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询用户简历分页列表\n"}, {"name": "getMyResumeList", "paramTypes": [], "doc": " 查询当前用户的所有简历列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.String"], "doc": " 获取用户简历详细信息\n\n @param resumeId 简历主键\n"}, {"name": "uploadResume", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": " 上传用户简历文件\n\n @param file     简历文件\n @param fileName 原始文件名（可选）\n @param fileType 文件类型（可选）\n"}, {"name": "add", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": " 新增用户简历\n"}, {"name": "edit", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": " 修改用户简历\n"}, {"name": "renameResume", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 重命名简历\n\n @param resumeId 简历ID\n @param request  重命名请求参数\n"}, {"name": "setDefaultResume", "paramTypes": ["java.lang.Long"], "doc": " 设置默认简历\n\n @param resumeId 简历ID\n"}, {"name": "cancelDefaultResume", "paramTypes": ["java.lang.Long"], "doc": " 取消默认简历\n\n @param resumeId 简历ID\n"}, {"name": "getDefaultResume", "paramTypes": [], "doc": " 获取当前用户的默认简历\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除用户简历\n\n @param resumeIds 简历主键串\n"}, {"name": "downloadResume", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 下载用户简历文件\n\n @param resumeId 简历ID\n"}, {"name": "previewResumeContent", "paramTypes": ["java.lang.Long"], "doc": " 预览简历文件内容\n\n @param resumeId 简历ID\n @return 预览内容响应\n"}], "constructors": []}