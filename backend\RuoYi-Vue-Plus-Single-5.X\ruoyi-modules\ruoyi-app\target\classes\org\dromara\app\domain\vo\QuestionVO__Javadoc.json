{"doc": " 题目VO\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 题目ID\n"}, {"name": "title", "doc": " 题目标题\n"}, {"name": "content", "doc": " 题目内容/描述\n"}, {"name": "description", "doc": " 题目描述（兼容字段）\n"}, {"name": "difficulty", "doc": " 难度（简单/中等/困难）\n"}, {"name": "type", "doc": " 题目类型（单选题/多选题/判断题/编程题/简答题）\n"}, {"name": "tags", "doc": " 标签列表\n"}, {"name": "acceptanceRate", "doc": " 通过率（百分比）\n"}, {"name": "isCompleted", "doc": " 是否已完成\n"}, {"name": "isBookmarked", "doc": " 是否已收藏\n"}, {"name": "practiceCount", "doc": " 练习次数\n"}, {"name": "correctRate", "doc": " 正确率（百分比）\n"}, {"name": "commentCount", "doc": " 评论数\n"}, {"name": "category", "doc": " 分类\n"}, {"name": "createTime", "doc": " 创建时间\n"}, {"name": "estimatedTime", "doc": " 预计完成时间（分钟）\n"}, {"name": "bankId", "doc": " 题库ID\n"}, {"name": "bankTitle", "doc": " 题库标题\n"}], "enumConstants": [], "methods": [], "constructors": []}