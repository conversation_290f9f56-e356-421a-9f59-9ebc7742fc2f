{"doc": " 向量嵌入实体\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 向量ID\n"}, {"name": "knowledgeBaseId", "doc": " 知识库ID\n"}, {"name": "documentId", "doc": " 文档ID\n"}, {"name": "content", "doc": " 文本块内容\n"}, {"name": "embedding", "doc": " 向量数据\n"}, {"name": "title", "doc": " 文本块标题\n"}, {"name": "summary", "doc": " 文本块摘要\n"}, {"name": "position", "doc": " 文本块在文档中的位置\n"}, {"name": "contentLength", "doc": " 文本块长度\n"}, {"name": "chunkType", "doc": " 文本块类型 (paragraph/heading/table/list/etc.)\n"}, {"name": "modelName", "doc": " 向量模型名称\n"}, {"name": "dimension", "doc": " 向量维度\n"}, {"name": "metadata", "doc": " 文本块元数据 (JSON格式)\n"}, {"name": "tags", "doc": " 文本块标签 (JSON数组)\n"}, {"name": "similarity", "doc": " 相似度分数 (查询时使用)\n"}, {"name": "sortOrder", "doc": " 排序字段\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}