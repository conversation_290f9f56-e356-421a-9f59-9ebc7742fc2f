{"doc": " AI代理服务接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getEnabledAgents", "paramTypes": [], "doc": " 获取所有启用的代理列表\n\n @return 代理列表\n"}, {"name": "getAgentByType", "paramTypes": ["java.lang.String"], "doc": " 根据类型获取代理\n\n @param agentType 代理类型\n @return 代理信息\n"}, {"name": "incrementUsageCount", "paramTypes": ["java.lang.String"], "doc": " 增加代理使用次数\n\n @param agentType 代理类型\n @return 是否成功\n"}, {"name": "updateRating", "paramTypes": ["java.lang.String", "java.lang.Double"], "doc": " 更新代理评分\n\n @param agentType 代理类型\n @param rating    评分\n @return 是否成功\n"}, {"name": "getQuickActions", "paramTypes": ["java.lang.String"], "doc": " 获取代理的快速操作列表\n\n @param agentType 代理类型\n @return 快速操作列表\n"}, {"name": "initDefaultAgents", "paramTypes": [], "doc": " 初始化默认代理数据\n"}, {"name": "enhanceQueryWithRAG", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 使用RAG增强用户查询\n\n @param agentType 代理类型\n @param userQuery 用户查询\n @return 增强后的查询内容\n"}, {"name": "getAgentKnowledgeBaseIds", "paramTypes": ["java.lang.String"], "doc": " 获取代理相关的知识库ID列表\n\n @param agentType 代理类型\n @return 知识库ID列表\n"}], "constructors": []}