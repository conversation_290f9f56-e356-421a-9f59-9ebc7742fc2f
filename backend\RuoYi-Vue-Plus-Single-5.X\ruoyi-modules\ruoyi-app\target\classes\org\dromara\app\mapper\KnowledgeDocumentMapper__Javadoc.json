{"doc": " 知识库文档Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": " 根据知识库ID查询文档列表\n\n @param knowledgeBaseId 知识库ID\n @return 文档列表\n"}, {"name": "selectByStatus", "paramTypes": ["java.lang.Integer"], "doc": " 根据状态查询文档列表\n\n @param status 状态\n @return 文档列表\n"}, {"name": "selectByProcessStatus", "paramTypes": ["java.lang.Integer"], "doc": " 根据处理状态查询文档列表\n\n @param processStatus 处理状态\n @return 文档列表\n"}, {"name": "updateStatus", "paramTypes": ["java.lang.Long", "java.lang.Integer", "java.lang.String"], "doc": " 更新文档状态\n\n @param id 文档ID\n @param status 状态\n @param errorMessage 错误信息\n @return 更新结果\n"}, {"name": "updateProcessStatus", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": " 更新处理状态\n\n @param id 文档ID\n @param processStatus 处理状态\n @return 更新结果\n"}, {"name": "updateVectorCount", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 更新向量数量\n\n @param id 文档ID\n @param vectorCount 向量数量\n @return 更新结果\n"}, {"name": "countByKnowledgeBaseId", "paramTypes": ["java.lang.Long"], "doc": " 统计知识库文档数量\n\n @param knowledgeBaseId 知识库ID\n @return 文档数量\n"}, {"name": "selectByDocType", "paramTypes": ["java.lang.String"], "doc": " 根据文档类型查询文档\n\n @param docType 文档类型\n @return 文档列表\n"}, {"name": "updateStatusByIds", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 批量更新文档状态\n\n @param ids 文档ID列表\n @param status 状态\n @return 更新结果\n"}], "constructors": []}