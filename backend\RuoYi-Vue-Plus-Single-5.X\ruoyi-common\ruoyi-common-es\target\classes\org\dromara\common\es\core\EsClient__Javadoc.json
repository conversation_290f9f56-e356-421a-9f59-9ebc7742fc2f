{"doc": " ES客户端\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createIndex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 创建索引\n\n @param indexName 索引名称\n @param mapping   映射配置\n @return 是否成功\n"}, {"name": "deleteIndex", "paramTypes": ["java.lang.String"], "doc": " 删除索引\n\n @param indexName 索引名称\n @return 是否成功\n"}, {"name": "existsIndex", "paramTypes": ["java.lang.String"], "doc": " 判断索引是否存在\n\n @param indexName 索引名称\n @return 是否存在\n"}, {"name": "addDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 添加文档\n\n @param indexName 索引名称\n @param id        文档ID\n @param document  文档内容\n @return 是否成功\n"}, {"name": "batchAddDocuments", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 批量添加文档\n\n @param indexName 索引名称\n @param documents 文档列表\n @return 是否成功\n"}, {"name": "updateDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 更新文档\n\n @param indexName 索引名称\n @param id        文档ID\n @param document  更新内容\n @return 是否成功\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 删除文档\n\n @param indexName 索引名称\n @param id        文档ID\n @return 是否成功\n"}, {"name": "getDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据ID获取文档\n\n @param indexName 索引名称\n @param id        文档ID\n @return 文档内容\n"}, {"name": "search", "paramTypes": ["org.dromara.common.es.entity.SearchRequest"], "doc": " 搜索文档\n\n @param searchRequest 搜索请求\n @return 搜索结果\n"}, {"name": "buildQuery", "paramTypes": ["org.dromara.common.es.entity.SearchRequest"], "doc": " 构建查询\n"}, {"name": "buildAggregation", "paramTypes": ["org.dromara.common.es.entity.SearchRequest.AggregationConfig"], "doc": " 构建聚合\n"}, {"name": "buildSearchResult", "paramTypes": ["co.elastic.clients.elasticsearch.core.SearchResponse", "java.lang.Integer", "java.lang.Integer"], "doc": " 构建搜索结果\n"}, {"name": "parseAggregationResult", "paramTypes": ["co.elastic.clients.elasticsearch._types.aggregations.Aggregate"], "doc": " 解析聚合结果\n"}], "constructors": []}