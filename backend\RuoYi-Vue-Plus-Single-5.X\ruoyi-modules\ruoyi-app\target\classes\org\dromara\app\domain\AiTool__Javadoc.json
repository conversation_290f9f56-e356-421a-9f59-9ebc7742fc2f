{"doc": " AI工具对象 app_ai_tool\n\n <AUTHOR>\n", "fields": [{"name": "id", "doc": " 工具ID\n"}, {"name": "name", "doc": " 工具名称\n"}, {"name": "displayName", "doc": " 工具显示名称\n"}, {"name": "description", "doc": " 工具描述\n"}, {"name": "category", "doc": " 工具分类：system/web/file/calculation/database/api\n"}, {"name": "icon", "doc": " 工具图标\n"}, {"name": "color", "doc": " 工具颜色\n"}, {"name": "functionDefinition", "doc": " 函数定义（JSON格式）\n"}, {"name": "parameterSchema", "doc": " 参数schema（JSON格式）\n"}, {"name": "implementationClass", "doc": " 实现类名\n"}, {"name": "toolConfig", "doc": " 工具配置（JSON格式）\n"}, {"name": "enabled", "doc": " 是否启用：0-禁用，1-启用\n"}, {"name": "isSystem", "doc": " 是否系统工具：0-自定义，1-系统内置\n"}, {"name": "permissionLevel", "doc": " 权限级别：0-公开，1-登录用户，2-特定权限\n"}, {"name": "requiredPermissions", "doc": " 所需权限\n"}, {"name": "usageCount", "doc": " 使用次数\n"}, {"name": "avgExecutionTime", "doc": " 平均执行时间（毫秒）\n"}, {"name": "successRate", "doc": " 成功率\n"}, {"name": "lastUsed", "doc": " 最后使用时间\n"}, {"name": "sortOrder", "doc": " 排序序号\n"}, {"name": "tags", "doc": " 标签，逗号分隔\n"}, {"name": "version", "doc": " 工具版本\n"}, {"name": "author", "doc": " 作者\n"}, {"name": "functionDefinitionObject", "doc": " 函数定义对象（不存储到数据库）\n"}, {"name": "parameterSchemaObject", "doc": " 参数Schema对象（不存储到数据库）\n"}, {"name": "toolConfigObject", "doc": " 工具配置对象（不存储到数据库）\n"}, {"name": "tagList", "doc": " 标签列表（不存储到数据库）\n"}], "enumConstants": [], "methods": [], "constructors": []}