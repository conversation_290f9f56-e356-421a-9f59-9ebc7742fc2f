{"doc": " 学习资源控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getMajorList", "paramTypes": [], "doc": " 获取专业列表\n\n @return 专业列表响应\n"}, {"name": "getQuestionBankList", "paramTypes": ["org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": " 获取题库列表\n\n @param queryDto 查询参数\n @return 题库列表响应\n"}, {"name": "toggleBookmark", "paramTypes": ["java.util.Map"], "doc": " 切换题库收藏状态\n\n @param request 收藏请求参数\n @return 收藏状态响应\n"}, {"name": "searchQuestionBanks", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 搜索题库\n\n @param keyword 搜索关键词\n @param majorId 专业ID\n @return 题库列表响应\n"}, {"name": "getQuestionBankDetail", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取题库详情\n\n @param bankId  题库ID\n @param majorId 专业ID\n @return 题库详情响应\n"}, {"name": "getHotQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": " 获取热门题库\n\n @param limit 限制数量\n @return 热门题库列表响应\n"}, {"name": "getNewQuestionBanks", "paramTypes": ["java.lang.Integer"], "doc": " 获取最新题库\n\n @param limit 限制数量\n @return 最新题库列表响应\n"}, {"name": "getQuestionBankFullDetail", "paramTypes": ["java.lang.String"], "doc": " 获取题库完整详情（包含学习进度等信息）\n\n @param bankId 题库ID\n @return 题库完整详情响应\n"}, {"name": "getQuestionsByCategory", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取题库分类题目\n\n @param bankId 题库ID\n @return 按分类组织的题目列表响应\n"}, {"name": "getRecommendedQuestions", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": " 获取推荐题目\n\n @param bankId 题库ID\n @param limit  限制数量\n @return 推荐题目列表响应\n"}, {"name": "toggleQuestionBankBookmark", "paramTypes": ["java.lang.String"], "doc": " 切换题库收藏状态（新版）\n\n @param bankId 题库ID\n @return 收藏状态响应\n"}, {"name": "getQuestionList", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.QuestionQueryDto"], "doc": " 获取题库题目列表（支持筛选和搜索）\n\n @param bankId   题库ID\n @param queryDto 查询参数\n @return 题目列表响应\n"}, {"name": "getQuestionStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取题库题目统计信息\n\n @param bankId 题库ID\n @return 统计信息响应\n"}, {"name": "getQuestionDetail", "paramTypes": ["java.lang.String"], "doc": " 获取题目详情\n\n @param questionId 题目ID\n @return 题目详情响应\n"}, {"name": "searchQuestions", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 搜索题目\n\n @param bankId     题库ID\n @param keyword    搜索关键词\n @param difficulty 难度等级\n @param category   分类\n @param completed  完成状态\n @return 题目列表响应\n"}, {"name": "toggleQuestionBookmark", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 切换题目收藏状态\n\n @param questionId 题目ID\n @param request    收藏请求参数\n @return 收藏状态响应\n"}, {"name": "getQuestionComments", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer", "java.lang.String", "java.lang.String"], "doc": " 获取题目评论列表\n\n @param questionId     题目ID\n @param page           页码\n @param pageSize       每页大小\n @param orderBy        排序字段\n @param orderDirection 排序方向\n @return 评论列表响应\n"}, {"name": "createQuestionComment", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 创建题目评论\n\n @param questionId 题目ID\n @param request    评论创建请求\n @return 创建结果响应\n"}, {"name": "getMajorQuestionBankList", "paramTypes": ["java.lang.String", "org.dromara.app.domain.dto.QuestionBankQueryDto"], "doc": " 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）\n 专为 all-question-banks.vue 页面设计\n\n @param queryDto 查询参数\n @return 题库列表响应\n"}, {"name": "getMajorQuestionBankStatistics", "paramTypes": ["java.lang.String"], "doc": " 获取专业题库统计信息\n 专为 all-question-banks.vue 页面设计\n\n @param majorId 专业ID\n @return 统计信息响应\n"}, {"name": "getMajorQuestionBankFilterCounts", "paramTypes": ["java.lang.String"], "doc": " 获取专业题库筛选选项计数\n 专为 all-question-banks.vue 页面设计\n\n @param majorId 专业ID\n @return 筛选选项计数响应\n"}, {"name": "resetMajorQuestionBankFilters", "paramTypes": ["java.lang.String"], "doc": " 重置专业题库筛选条件\n 专为 all-question-banks.vue 页面设计\n\n @param majorId 专业ID\n @return 重置结果响应\n"}], "constructors": []}