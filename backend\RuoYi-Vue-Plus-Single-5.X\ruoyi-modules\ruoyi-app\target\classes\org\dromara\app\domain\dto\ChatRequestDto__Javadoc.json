{"doc": " 聊天请求DTO\n\n <AUTHOR>\n", "fields": [{"name": "message", "doc": " 消息内容\n"}, {"name": "sessionId", "doc": " 会话ID（可选，新会话时为空）\n"}, {"name": "agentType", "doc": " Agent类型\n"}, {"name": "attachments", "doc": " 附件列表\n"}, {"name": "messageType", "doc": " 消息类型：text/image/voice\n"}, {"name": "provider", "doc": " 模型提供商：ollama/openai/claude\n"}, {"name": "modelName", "doc": " 模型名称\n"}, {"name": "temperature", "doc": " 温度参数\n"}, {"name": "enableStreaming", "doc": " 是否启用流式响应\n"}, {"name": "contextLimit", "doc": " 上下文消息数量限制\n"}], "enumConstants": [], "methods": [], "constructors": []}