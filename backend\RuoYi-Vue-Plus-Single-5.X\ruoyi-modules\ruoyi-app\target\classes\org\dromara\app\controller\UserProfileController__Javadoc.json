{"doc": " 用户个人信息Controller\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "profile", "paramTypes": [], "doc": " 获取个人信息\n"}, {"name": "updateProfile", "paramTypes": ["org.dromara.app.domain.dto.AppUserProfileDto"], "doc": " 修改个人信息\n"}, {"name": "avatar", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 头像上传\n"}, {"name": "resetProfile", "paramTypes": [], "doc": " 重置个人信息\n"}], "constructors": []}