{"doc": " AI工具管理控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getAvailableTools", "paramTypes": ["java.lang.String"], "doc": " 获取可用工具列表\n"}, {"name": "getToolDetail", "paramTypes": ["java.lang.String"], "doc": " 获取工具详情\n"}, {"name": "executeTool", "paramTypes": ["org.dromara.app.controller.ToolController.ToolExecuteRequest"], "doc": " 执行工具调用\n"}, {"name": "executeToolAsync", "paramTypes": ["org.dromara.app.controller.ToolController.ToolExecuteRequest"], "doc": " 异步执行工具调用\n"}, {"name": "executeBatchTools", "paramTypes": ["org.dromara.app.controller.ToolController.BatchToolExecuteRequest"], "doc": " 批量执行工具调用\n"}, {"name": "validateParameters", "paramTypes": ["org.dromara.app.controller.ToolController.ParameterValidationRequest"], "doc": " 验证工具调用参数\n"}, {"name": "getToolCallHistory", "paramTypes": ["org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取工具调用历史\n"}, {"name": "getSessionToolCalls", "paramTypes": ["java.lang.String"], "doc": " 获取会话工具调用记录\n"}, {"name": "getToolCallDetail", "paramTypes": ["java.lang.String"], "doc": " 获取工具调用详情\n"}, {"name": "retryToolCall", "paramTypes": ["java.lang.String"], "doc": " 重试工具调用\n"}, {"name": "getToolUsageStats", "paramTypes": [], "doc": " 获取工具使用统计\n"}, {"name": "getToolPerformanceStats", "paramTypes": ["java.lang.String"], "doc": " 获取工具性能统计\n"}, {"name": "registerTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": " 注册工具\n"}, {"name": "updateTool", "paramTypes": ["org.dromara.app.domain.AiTool"], "doc": " 更新工具\n"}, {"name": "deleteTool", "paramTypes": ["java.lang.String"], "doc": " 删除工具\n"}, {"name": "toggleTool", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 启用/禁用工具\n"}, {"name": "initSystemTools", "paramTypes": [], "doc": " 初始化系统工具\n"}], "constructors": []}