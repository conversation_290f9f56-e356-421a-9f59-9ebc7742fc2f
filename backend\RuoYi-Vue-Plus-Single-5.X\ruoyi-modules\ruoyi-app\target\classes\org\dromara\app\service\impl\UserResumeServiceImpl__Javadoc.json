{"doc": " 用户简历服务实现\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryPageList", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询用户简历分页列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 分页结果\n"}, {"name": "selectUserResumeList", "paramTypes": ["java.lang.Long"], "doc": " 查询用户的所有简历列表\n\n @param userId 用户ID\n @return 简历列表\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 根据简历ID查询简历详情\n\n @param resumeId 简历ID\n @return 简历详情\n"}, {"name": "uploadResume", "paramTypes": ["java.lang.Long", "org.springframework.web.multipart.MultipartFile", "java.lang.String", "java.lang.String"], "doc": " 上传用户简历文件\n\n @param userId   用户ID\n @param file     简历文件\n @param fileName 原始文件名（可选）\n @param fileType 文件类型（可选）\n @return 上传结果\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": " 新增用户简历\n\n @param bo 简历信息\n @return 新增结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": " 修改用户简历\n\n @param bo 简历信息\n @return 修改结果\n"}, {"name": "renameResume", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 重命名简历\n\n @param resumeId   简历ID\n @param resumeName 新名称\n @return 重命名结果\n"}, {"name": "setDefaultResume", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 设置默认简历\n\n @param userId   用户ID\n @param resumeId 简历ID\n @return 设置结果\n"}, {"name": "cancelDefaultResume", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 取消默认简历\n\n @param userId   用户ID\n @param resumeId 简历ID\n @return 取消结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除用户简历信息\n\n @param ids     主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 删除结果\n"}, {"name": "downloadResume", "paramTypes": ["java.lang.Long", "jakarta.servlet.http.HttpServletResponse"], "doc": " 下载用户简历文件\n\n @param resumeId 简历ID\n @param response 响应对象\n @throws IOException IO异常\n"}, {"name": "getDefaultResume", "paramTypes": ["java.lang.Long"], "doc": " 获取用户的默认简历\n\n @param userId 用户ID\n @return 默认简历\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.app.domain.UserResume"], "doc": " 保存前的数据校验\n\n @param entity 实体类数据\n"}, {"name": "buildQueryWrapper", "paramTypes": ["org.dromara.app.domain.bo.UserResumeBo"], "doc": " 构建查询条件\n\n @param bo 业务对象\n @return 查询条件\n"}, {"name": "formatFileSize", "paramTypes": ["org.dromara.app.domain.vo.UserResumeVo"], "doc": " 格式化文件大小\n\n @param vo 视图对象\n"}, {"name": "formatFileSize", "paramTypes": ["java.lang.Long"], "doc": " 格式化文件大小\n\n @param size 文件大小(字节)\n @return 格式化后的大小字符串\n"}, {"name": "previewResumeContent", "paramTypes": ["java.lang.Long"], "doc": " 预览简历文件内容\n\n @param resumeId 简历ID\n @return 预览内容数据\n @throws Exception 预览异常\n"}, {"name": "extractPdfContent", "paramTypes": ["org.dromara.system.domain.vo.SysOssVo"], "doc": " 提取PDF文件内容\n"}, {"name": "extractDocContent", "paramTypes": ["org.dromara.system.domain.vo.SysOssVo"], "doc": " 提取DOC文件内容\n"}, {"name": "extractDocxContent", "paramTypes": ["org.dromara.system.domain.vo.SysOssVo"], "doc": " 提取DOCX文件内容\n"}, {"name": "getFileInputStream", "paramTypes": ["org.dromara.system.domain.vo.SysOssVo"], "doc": " 获取OSS文件的输入流\n"}], "constructors": []}