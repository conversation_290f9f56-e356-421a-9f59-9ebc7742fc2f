{"doc": " 租户助手\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "isEnable", "paramTypes": [], "doc": " 租户功能是否启用\n"}, {"name": "enableIgnore", "paramTypes": [], "doc": " 开启忽略租户(开启后需手动调用 {@link #disableIgnore()} 关闭)\n"}, {"name": "disableIgnore", "paramTypes": [], "doc": " 关闭忽略租户\n"}, {"name": "ignore", "paramTypes": ["java.lang.Runnable"], "doc": " 在忽略租户中执行\n\n @param handle 处理执行方法\n"}, {"name": "ignore", "paramTypes": ["java.util.function.Supplier"], "doc": " 在忽略租户中执行\n\n @param handle 处理执行方法\n"}, {"name": "setDynamic", "paramTypes": ["java.lang.String"], "doc": " 设置动态租户(一直有效 需要手动清理)\n <p>\n 如果为非web环境 那么只在当前线程内生效\n"}, {"name": "setDynamic", "paramTypes": ["java.lang.String", "boolean"], "doc": " 设置动态租户(一直有效 需要手动清理)\n <p>\n 如果为未登录状态下 那么只在当前线程内生效\n\n @param tenantId 租户id\n @param global   是否全局生效\n"}, {"name": "getDynamic", "paramTypes": [], "doc": " 获取动态租户(一直有效 需要手动清理)\n <p>\n 如果为非web环境 那么只在当前线程内生效\n"}, {"name": "clearDynamic", "paramTypes": [], "doc": " 清除动态租户\n"}, {"name": "getTenantId", "paramTypes": [], "doc": " 获取当前租户id(动态租户优先)\n"}, {"name": "dynamic", "paramTypes": ["java.lang.String", "java.lang.Runnable"], "doc": " 在动态租户中执行\n\n @param handle 处理执行方法\n"}], "constructors": []}