{"doc": " 知识库Mapper接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectEnabledKnowledgeBases", "paramTypes": [], "doc": " 查询启用的知识库列表\n\n @return 知识库列表\n"}, {"name": "selectByType", "paramTypes": ["java.lang.String"], "doc": " 根据类型查询知识库\n\n @param type 知识库类型\n @return 知识库列表\n"}, {"name": "updateDocumentCount", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 更新文档数量\n\n @param knowledgeBaseId 知识库ID\n @param documentCount 文档数量\n @return 更新结果\n"}, {"name": "updateVectorCount", "paramTypes": ["java.lang.Long", "java.lang.Long"], "doc": " 更新向量数量\n\n @param knowledgeBaseId 知识库ID\n @param vectorCount 向量数量\n @return 更新结果\n"}, {"name": "selectByUserId", "paramTypes": ["java.lang.Long"], "doc": " 根据用户ID查询知识库\n\n @param userId 用户ID\n @return 知识库列表\n"}, {"name": "updateStatusByIds", "paramTypes": ["java.util.List", "java.lang.Integer"], "doc": " 批量更新知识库状态\n\n @param ids 知识库ID列表\n @param status 状态\n @return 更新结果\n"}], "constructors": []}