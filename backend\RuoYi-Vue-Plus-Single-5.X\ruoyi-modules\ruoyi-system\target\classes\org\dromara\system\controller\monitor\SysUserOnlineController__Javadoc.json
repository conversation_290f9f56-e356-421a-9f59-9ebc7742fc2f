{"doc": " 在线用户监控\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 获取在线用户监控列表\n\n @param ipaddr   IP地址\n @param userName 用户名\n"}, {"name": "forceLogout", "paramTypes": ["java.lang.String"], "doc": " 强退用户\n\n @param tokenId token值\n"}, {"name": "getInfo", "paramTypes": [], "doc": " 获取当前用户登录在线设备\n"}, {"name": "remove", "paramTypes": ["java.lang.String"], "doc": " 强退当前在线设备\n\n @param tokenId token值\n"}], "constructors": []}