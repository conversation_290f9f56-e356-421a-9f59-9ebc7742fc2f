{"doc": " Caffeine缓存注解切面\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleCaffeineCache", "paramTypes": ["org.aspectj.lang.ProceedingJoinPoint", "org.dromara.common.caffeine.annotation.CaffeineCache"], "doc": " @CaffeineCache注解处理\n"}, {"name": "handleCaffeinePut", "paramTypes": ["org.aspectj.lang.ProceedingJoinPoint", "org.dromara.common.caffeine.annotation.CaffeinePut"], "doc": " @CaffeinePut注解处理\n"}, {"name": "handleCaffeineEvict", "paramTypes": ["org.aspectj.lang.ProceedingJoinPoint", "org.dromara.common.caffeine.annotation.CaffeineEvict"], "doc": " @CaffeineEvict注解处理\n"}, {"name": "evictCache", "paramTypes": ["org.dromara.common.caffeine.annotation.CaffeineEvict", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": " 清除缓存\n"}, {"name": "generate<PERSON>ache<PERSON>ey", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": " 生成缓存键\n"}, {"name": "checkCondition", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": " 检查条件\n"}, {"name": "checkCondition", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": " 检查条件\n"}, {"name": "parseTtl", "paramTypes": ["java.lang.String", "org.dromara.common.caffeine.annotation.TimeUnit", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": " 解析过期时间\n\n @param expireExpression 过期时间表达式\n @param timeUnit         时间单位\n @param method           方法\n @param args             参数\n @param result           返回值\n @return 过期时间（秒），-1表示使用默认配置，0表示永不过期\n"}], "constructors": []}