{"doc": " ES工具类\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "createIndex", "paramTypes": ["java.lang.String"], "doc": " 创建索引\n\n @param indexName 索引名称\n @return 是否成功\n"}, {"name": "createIndex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 创建索引\n\n @param indexName 索引名称\n @param mapping   映射配置\n @return 是否成功\n"}, {"name": "deleteIndex", "paramTypes": ["java.lang.String"], "doc": " 删除索引\n\n @param indexName 索引名称\n @return 是否成功\n"}, {"name": "existsIndex", "paramTypes": ["java.lang.String"], "doc": " 判断索引是否存在\n\n @param indexName 索引名称\n @return 是否存在\n"}, {"name": "addDocument", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": " 添加文档\n\n @param indexName 索引名称\n @param document  文档内容\n @return 是否成功\n"}, {"name": "addDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 添加文档\n\n @param indexName 索引名称\n @param id        文档ID\n @param document  文档内容\n @return 是否成功\n"}, {"name": "batchAddDocuments", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 批量添加文档\n\n @param indexName 索引名称\n @param documents 文档列表\n @return 是否成功\n"}, {"name": "updateDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": " 更新文档\n\n @param indexName 索引名称\n @param id        文档ID\n @param document  更新内容\n @return 是否成功\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 删除文档\n\n @param indexName 索引名称\n @param id        文档ID\n @return 是否成功\n"}, {"name": "getDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据ID获取文档\n\n @param indexName 索引名称\n @param id        文档ID\n @return 文档内容\n"}, {"name": "search", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 搜索文档\n\n @param indexName 索引名称\n @param keyword   关键字\n @return 搜索结果\n"}, {"name": "search", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 搜索文档\n\n @param indexName 索引名称\n @param keyword   关键字\n @param pageNum   页码\n @param pageSize  页大小\n @return 搜索结果\n"}, {"name": "search", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.List"], "doc": " 搜索文档\n\n @param indexName 索引名称\n @param keyword   关键字\n @param fields    查询字段\n @return 搜索结果\n"}, {"name": "search", "paramTypes": ["org.dromara.common.es.entity.SearchRequest"], "doc": " 搜索文档\n\n @param searchRequest 搜索请求\n @return 搜索结果\n"}, {"name": "searchWithHighlight", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.List"], "doc": " 高亮搜索\n\n @param indexName       索引名称\n @param keyword         关键字\n @param highlightFields 高亮字段\n @return 搜索结果\n"}, {"name": "searchWithFilters", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 过滤搜索\n\n @param indexName 索引名称\n @param filters   过滤条件\n @return 搜索结果\n"}, {"name": "searchWithAggregations", "paramTypes": ["java.lang.String", "java.util.List"], "doc": " 聚合搜索\n\n @param indexName    索引名称\n @param aggregations 聚合配置\n @return 搜索结果\n"}, {"name": "builder", "paramTypes": ["java.lang.String"], "doc": " 构建搜索请求\n\n @param indexName 索引名称\n @return 搜索请求构建器\n"}], "constructors": []}