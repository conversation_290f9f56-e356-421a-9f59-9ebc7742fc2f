{"doc": " 对象存储配置Service接口\n\n <AUTHOR>\n <AUTHOR>\n @date 2021-08-13\n", "fields": [], "enumConstants": [], "methods": [{"name": "init", "paramTypes": [], "doc": " 初始化OSS配置\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询单个\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": " 根据新增业务对象插入对象存储配置\n\n @param bo 对象存储配置新增业务对象\n @return 结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": " 根据编辑业务对象修改对象存储配置\n\n @param bo 对象存储配置编辑业务对象\n @return 结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并删除数据\n\n @param ids     主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 结果\n"}, {"name": "updateOssConfigStatus", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": " 启用停用状态\n"}], "constructors": []}