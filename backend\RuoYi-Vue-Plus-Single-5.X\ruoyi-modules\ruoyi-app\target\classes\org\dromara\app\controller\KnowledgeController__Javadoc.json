{"doc": " 知识库管理Controller\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "getKnowledgeBases", "paramTypes": [], "doc": " 获取知识库列表\n"}, {"name": "getKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库详情\n"}, {"name": "createKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": " 创建知识库\n"}, {"name": "updateKnowledgeBase", "paramTypes": ["org.dromara.app.domain.KnowledgeBase"], "doc": " 更新知识库\n"}, {"name": "deleteKnowledgeBase", "paramTypes": ["java.lang.Long"], "doc": " 删除知识库\n"}, {"name": "getKnowledgeBaseStats", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库统计信息\n"}, {"name": "rebuildIndex", "paramTypes": ["java.lang.Long"], "doc": " 重建知识库索引\n"}, {"name": "getDocuments", "paramTypes": ["java.lang.Long"], "doc": " 获取知识库文档列表\n"}, {"name": "addDocument", "paramTypes": ["org.dromara.app.domain.KnowledgeDocument"], "doc": " 添加文档到知识库\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.Long"], "doc": " 删除文档\n"}, {"name": "processDocument", "paramTypes": ["java.lang.Long"], "doc": " 处理文档（重新向量化）\n"}, {"name": "searchKnowledge", "paramTypes": ["org.dromara.app.controller.KnowledgeController.SearchRequest"], "doc": " 搜索知识库\n"}, {"name": "hybridSearch", "paramTypes": ["org.dromara.app.controller.KnowledgeController.SearchRequest"], "doc": " 混合搜索知识库\n"}], "constructors": []}