{"doc": " AI聊天控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "sendMessage", "paramTypes": ["org.dromara.app.domain.dto.ChatRequestDto"], "doc": " 发送聊天消息（同步响应）\n\n @param request 聊天请求\n @return 聊天响应\n"}, {"name": "sendMessageStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": " 发送聊天消息（流式响应）\n\n @param request 聊天请求\n @return SSE流\n"}, {"name": "createSession", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 创建新会话\n\n @param agentType 代理类型\n @param title     会话标题（可选）\n @return 会话信息\n"}, {"name": "getUserSessions", "paramTypes": ["java.lang.Integer", "java.lang.Integer"], "doc": " 获取用户会话列表\n\n @param pageNum  页码\n @param pageSize 每页大小\n @return 会话分页结果\n"}, {"name": "getSessionDetail", "paramTypes": ["java.lang.String"], "doc": " 获取会话详情\n\n @param sessionId 会话ID\n @return 会话详情\n"}, {"name": "getSessionMessages", "paramTypes": ["java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": " 获取会话消息列表\n\n @param sessionId 会话ID\n @param pageNum   页码\n @param pageSize  每页大小\n @return 消息分页结果\n"}, {"name": "deleteSession", "paramTypes": ["java.lang.String"], "doc": " 删除会话\n\n @param sessionId 会话ID\n @return 操作结果\n"}, {"name": "clearSessionMessages", "paramTypes": ["java.lang.String"], "doc": " 清空会话消息\n\n @param sessionId 会话ID\n @return 操作结果\n"}, {"name": "updateSessionTitle", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 更新会话标题\n\n @param sessionId 会话ID\n @param title     新标题\n @return 操作结果\n"}, {"name": "archiveSession", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": " 归档/取消归档会话\n\n @param sessionId 会话ID\n @param archived  是否归档\n @return 操作结果\n"}, {"name": "getUserChatStats", "paramTypes": [], "doc": " 获取用户聊天统计信息\n\n @return 统计信息\n"}, {"name": "getEnabledAgents", "paramTypes": [], "doc": " 获取所有启用的代理列表\n\n @return 代理列表\n"}, {"name": "getAgentByType", "paramTypes": ["java.lang.String"], "doc": " 根据类型获取代理信息\n\n @param agentType 代理类型\n @return 代理信息\n"}, {"name": "getQuickActions", "paramTypes": ["java.lang.String"], "doc": " 获取代理的快速操作列表\n\n @param agentType 代理类型\n @return 快速操作列表\n"}, {"name": "callAgent", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map"], "doc": " 调用特定类型的Agent进行处理（同步响应）\n\n @param agentType 代理类型\n @param message 用户消息\n @param params 额外参数\n @return 处理结果\n"}, {"name": "callAgentStream", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.Map", "java.lang.String"], "doc": " 调用特定类型的Agent进行处理（流式响应）\n\n @param agentType 代理类型\n @param message 用户消息\n @param params 额外参数\n @return SSE流\n"}, {"name": "uploadFile", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String"], "doc": " 上传聊天附件\n\n @param file 文件\n @param type 文件类型：image/file/voice\n @return 上传结果\n"}, {"name": "speechToText", "paramTypes": ["org.springframework.web.multipart.MultipartFile"], "doc": " 语音转文字\n\n @param audioFile 音频文件\n @return 转换结果\n"}, {"name": "getAvailableProviders", "paramTypes": [], "doc": " 获取可用的AI提供商列表\n\n @return 提供商列表\n"}, {"name": "getAgentStats", "paramTypes": [], "doc": " 获取Agent使用统计\n\n @return 使用统计\n"}], "constructors": []}