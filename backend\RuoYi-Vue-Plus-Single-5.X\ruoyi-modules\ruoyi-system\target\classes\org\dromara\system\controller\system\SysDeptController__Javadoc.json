{"doc": " 部门信息\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 获取部门列表\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long"], "doc": " 查询部门列表（排除节点）\n\n @param deptId 部门ID\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据部门编号获取详细信息\n\n @param deptId 部门ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 新增部门\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 修改部门\n"}, {"name": "remove", "paramTypes": ["java.lang.Long"], "doc": " 删除部门\n\n @param deptId 部门ID\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]"], "doc": " 获取部门选择框列表\n\n @param deptIds 部门ID串\n"}], "constructors": []}